import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUnknownStatusToDeviceStatusEnum1703604000000 implements MigrationInterface {
    name = 'AddUnknownStatusToDeviceStatusEnum1703604000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add 'unknown' to the device_status_enum
        await queryRunner.query(`
            ALTER TYPE "public"."device_status_enum" ADD VALUE 'unknown'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Note: PostgreSQL doesn't support removing enum values directly
        // This would require recreating the enum and updating all references
        // For now, we'll leave the enum value in place
        console.log('Cannot remove enum value in PostgreSQL. Manual intervention required.');
    }
}
